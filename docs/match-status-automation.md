# Match Status Automation

## Overview

The match status system has been enhanced with automatic status updates for delayed matches. When a match's scheduled date passes without the match being started, it will automatically be marked as "delayed".

## Match Status Enum

The `status` column in the `matches` table now uses an enum type with the following values:

- `scheduled` - Match is scheduled for a future date/time
- `delayed` - Match was scheduled but the scheduled time has passed
- `in_progress` - Match is currently being played
- `completed` - Match has finished
- `cancelled` - Match has been cancelled

## Automatic Status Updates

### Database Trigger

A database trigger automatically updates match status from `scheduled` to `delayed` when:

1. A new match is inserted with a past `scheduled_date`
2. An existing match's `scheduled_date` is updated to a past date
3. An existing match's status is updated to `scheduled` but the `scheduled_date` is in the past

The trigger runs on `BEFORE INSERT OR UPDATE` and ensures data consistency at the database level.

### Manual Update Function

For additional reliability, a database function `update_delayed_matches_api()` can be called to batch update all delayed matches:

```typescript
import { updateDelayedMatches } from '@/services/matchStatusService';

const result = await updateDelayedMatches();
if (result.success) {
  console.log(`Updated ${result.updated_matches} matches to delayed status`);
}
```

## Frontend Integration

### TypeScript Types

The `MatchStatus` type in `types/matches.ts` includes the new `DELAYED` constant:

```typescript
import { DELAYED } from '@/types/matches';
```

### UI Components

- **MatchCard**: Displays delayed matches with orange background color
- **Match Utils**: Provides appropriate text for delayed matches showing how long they've been delayed

### Status Colors

- Scheduled: Blue (`bg-blue-500`)
- Delayed: Orange (`bg-orange-500`)
- In Progress: Green (`bg-green-500`)
- Completed: Gray (`bg-gray-500`)
- Cancelled: Red (`bg-red-500`)

## Usage Examples

### Checking if a match should be delayed (client-side)

```typescript
import { shouldMatchBeDelayed } from '@/services/matchStatusService';

const isDelayed = shouldMatchBeDelayed(match.scheduled_date, match.status);
```

### Getting appropriate status based on date

```typescript
import { getMatchStatusBasedOnDate } from '@/services/matchStatusService';

const actualStatus = getMatchStatusBasedOnDate(match.scheduled_date, match.status);
```

## Database Schema

### Enum Type
```sql
CREATE TYPE match_status AS ENUM ('scheduled', 'delayed', 'in_progress', 'completed', 'cancelled');
```

### Trigger Function
```sql
CREATE OR REPLACE FUNCTION check_match_delay_trigger()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.status = 'scheduled' AND NEW.scheduled_date IS NOT NULL THEN
    IF NEW.scheduled_date < NOW() THEN
      NEW.status = 'delayed';
      NEW.updated_at = NOW();
    END IF;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

### Trigger
```sql
CREATE TRIGGER trigger_check_match_delay
  BEFORE INSERT OR UPDATE ON matches
  FOR EACH ROW
  EXECUTE FUNCTION check_match_delay_trigger();
```

## Benefits

1. **Automatic Status Management**: No manual intervention needed for delayed matches
2. **Data Consistency**: Database-level enforcement ensures status accuracy
3. **Real-time Updates**: Triggers fire immediately on data changes
4. **Batch Processing**: Manual function available for bulk updates
5. **UI Clarity**: Clear visual indication of delayed matches for users
