import React from "react";
import { <PERSON>, ScrollView } from "react-native";
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
  ActionsheetDragIndicator,
  ActionsheetDragIndicatorWrapper,
} from "@/components/ui/actionsheet";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { Divider } from "@/components/ui/divider";
import { Match } from "../../../types/matches";
import { ParticipantDetails } from "../../../types/participants";
import ParticipantDisplay from "./ParticipantDisplay";
import { CTAButton } from "@/components/ui/primaryCTAbutton";
import RenderIf from "@/components/util-components/RenderIf";
import { getMatchStatusText } from "../utils/matchUtils";
import {
  getMatchStatusBasedOnDate,
  getStatusDisplayText,
  getStatusColor,
} from "@/services/matchStatusService";

export interface MatchDetailsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  match: Match;
  participant1Details?: ParticipantDetails | null;
  participant2Details?: ParticipantDetails | null;
}

const MatchDetailsDialog: React.FC<MatchDetailsDialogProps> = ({
  isOpen,
  onClose,
  match,
  participant1Details,
  participant2Details,
}) => {
  const matchStatusText = getMatchStatusText(
    match.scheduled_date,
    match.status
  );

  // Get the actual status for display (including client-side delayed logic)
  const actualStatus = getMatchStatusBasedOnDate(
    match.scheduled_date,
    match.status
  );
  const statusDisplayText = getStatusDisplayText(actualStatus);
  const statusColor = getStatusColor(actualStatus);
  return (
    <Actionsheet isOpen={isOpen} onClose={onClose}>
      <ActionsheetBackdrop />
      <ActionsheetContent>
        <ActionsheetDragIndicatorWrapper>
          <ActionsheetDragIndicator />
        </ActionsheetDragIndicatorWrapper>

        <ScrollView
          className="w-full mt-3"
          showsVerticalScrollIndicator={false}
        >
          <VStack className="">
            {/* Participants Section */}
            <VStack className="space-y-4 p-4">
              <HStack className="items-center justify-between">
                <View className="flex-1 max-w-[45%]">
                  <ParticipantDisplay
                    participantId={match.participant_1_id}
                    participantType={match.participant_type}
                    participantName={match.participant_1_name}
                    fallbackName="TBD"
                    preloadedParticipant={participant1Details}
                  />
                </View>

                <View className="px-4">
                  <Text className="text-xl font-urbanistExtraBold text-gray-400 tracking-widest">
                    VS
                  </Text>
                </View>

                <View className="flex-1 max-w-[45%]">
                  <ParticipantDisplay
                    participantId={match.participant_2_id}
                    participantType={match.participant_type}
                    participantName={match.participant_2_name}
                    fallbackName="TBD"
                    preloadedParticipant={participant2Details}
                  />
                </View>
              </HStack>
            </VStack>
            <Divider className="w-1/2 items-center self-center" />
            <View className="flex flex-row gap-2 items-center space-x-3 self-center py-2 mb-6">
              {/* Status Badge */}
              <View className={`px-3 py-1 rounded-full ${statusColor}`}>
                <Text className="text-white text-xs font-urbanistBold uppercase">
                  {statusDisplayText}
                </Text>
              </View>

              {/* Status Description */}
              <Text className="text-sm font-urbanistMedium text-gray-800">
                {matchStatusText}
              </Text>

              <RenderIf condition={!!match?.court_field_number}>
                <View className="w-1 h-1 bg-gray-500 rounded-full" />
                <Text className="text-sm font-urbanistMedium text-gray-800">
                  {match.court_field_number}
                </Text>
              </RenderIf>
            </View>

            <View className="border-t border-gray-300 border-dashed" />
            <VStack className="space-y-4 p-4 gap-2">
              <CTAButton title="Edit Match" onPress={onClose} />
              <CTAButton title="Cancel Match" onPress={onClose} />
            </VStack>
          </VStack>
        </ScrollView>
      </ActionsheetContent>
    </Actionsheet>
  );
};

export default MatchDetailsDialog;
