import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
dayjs.extend(relativeTime);

export function getMatchStatusText(scheduledDate: string | null, status: string): string {
  if (!scheduledDate || !dayjs(scheduledDate).isValid()) {
    return "Match date not available";
  }

  const now = dayjs();
  const matchTime = dayjs(scheduledDate);
  const isPast = matchTime.isBefore(now);
  const diffMinutes = Math.abs(matchTime.diff(now, 'minute'));
  const diffHours = Math.abs(matchTime.diff(now, 'hour'));
  const diffDays = Math.abs(matchTime.diff(now, 'day'));

  const formattedDate = matchTime.format('MMM Do, YYYY'); // e.g. Jul 25th, 2025

  switch (status) {
    case 'scheduled': {
      if (isPast) {
        return `Match was scheduled for ${formattedDate}`;
      } else if (diffMinutes < 60) {
        return `Match starts in ${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`;
      } else if (diffHours < 24) {
        return `Match starts in ${diffHours} hour${diffHours > 1 ? 's' : ''}`;
      } else if (diffDays < 30) {
        return `Match scheduled for ${formattedDate}`;
      } else {
        return `Match scheduled for ${formattedDate}`;
      }
    }

    case 'delayed': {
      if (isPast) {
        if (diffMinutes < 60) {
          return `Match delayed by ${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`;
        } else if (diffHours < 24) {
          return `Match delayed by ${diffHours} hour${diffHours > 1 ? 's' : ''}`;
        } else if (diffDays < 30) {
          return `Match delayed by ${diffDays} day${diffDays > 1 ? 's' : ''}`;
        } else {
          return `Match delayed (originally scheduled for ${formattedDate})`;
        }
      } else {
        return `Match is delayed and rescheduled for ${formattedDate}`;
      }
    }

    case 'in_progress':
      return 'Match is live';

    case 'completed':
      return `Match completed on ${formattedDate}`;

    case 'cancelled':
      return 'Match has been cancelled';

    default:
      return `Match status: ${status}`;
  }
}
