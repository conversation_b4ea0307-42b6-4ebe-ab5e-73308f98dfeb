export type UUID = string;
export type JsonObject = Record<string, unknown>;

// Import participant type from centralized location
import type { ParticipantType } from './participants';
export type { ParticipantType };

export const SCHEDULED = 'scheduled';
export const DELAYED = 'delayed';
export const IN_PROGRESS = 'in_progress';
export const COMPLETED = 'completed';
export const CANCELLED = 'cancelled';

export type MatchStatus =
  | typeof SCHEDULED
  | typeof DELAYED
  | typeof IN_PROGRESS
  | typeof COMPLETED
  | typeof CANCELLED;

export interface Match {
  id: UUID;
  tournament_id: UUID;
  match_number: number | null;
  round_name: string | null;
  stage: string | null;
  participant_type: ParticipantType;
  participant_1_id: UUID | null;
  participant_1_name: string | null;
  participant_2_id: UUID | null;
  participant_2_name: string | null;
  scheduled_date: string | null; // ISO timestamp string
  actual_start_time: string | null; // ISO timestamp string
  actual_end_time: string | null; // ISO timestamp string
  match_venue: string | null;
  court_field_number: string | null;
  status: MatchStatus;
  participant_1_score: number | null;
  participant_2_score: number | null;
  detailed_results: JsonObject | null;
  winner_id: UUID | null;
  winner_type: ParticipantType | null;
  is_walkover: boolean;
  match_summary: string | null;
  duration_minutes: number | null;
  notes: string | null;
  referee_name: string | null;
  created_at: string; // ISO timestamp string
  updated_at: string; // ISO timestamp string
  created_by: UUID | null;
}

// Input type for creating a match
export interface CreateMatchInput {
  tournament_id: UUID;
  participant_type: ParticipantType;
  participant_1_id?: UUID | null;
  participant_1_name?: string | null;
  participant_2_id?: UUID | null;
  participant_2_name?: string | null;
  scheduled_date?: string | null;
  stage?: string | null;
  court_field_number?: string | null;
  match_number?: number | null;
  round_name?: string | null;
  match_venue?: string | null;
  status?: MatchStatus;
}
