import { supabase } from '@/lib/supabase';

export interface UpdateDelayedMatchesResult {
  success: boolean;
  updated_matches?: number;
  error?: string;
  timestamp?: string;
}

/**
 * Manually trigger update of delayed matches
 * This calls the database function to update all scheduled matches
 * where the scheduled_date has passed to 'delayed' status
 */
export async function updateDelayedMatches(): Promise<UpdateDelayedMatchesResult> {
  try {
    const { data, error } = await supabase.rpc('update_delayed_matches_api');

    if (error) {
      console.error('[updateDelayedMatches] Error:', error);
      return {
        success: false,
        error: error.message,
      };
    }

    return data as UpdateDelayedMatchesResult;
  } catch (error: any) {
    console.error('[updateDelayedMatches] Unexpected error:', error);
    return {
      success: false,
      error: error.message || 'Failed to update delayed matches',
    };
  }
}

/**
 * Check if a specific match should be marked as delayed
 * This is a client-side utility function
 */
export function shouldMatchBeDelayed(scheduledDate: string | null, currentStatus: string): boolean {
  if (!scheduledDate || currentStatus !== 'scheduled') {
    return false;
  }

  const now = new Date();
  const matchDate = new Date(scheduledDate);
  
  return matchDate < now;
}

/**
 * Get the appropriate status for a match based on its scheduled date
 */
export function getMatchStatusBasedOnDate(scheduledDate: string | null, currentStatus: string): string {
  if (shouldMatchBeDelayed(scheduledDate, currentStatus)) {
    return 'delayed';
  }
  return currentStatus;
}
